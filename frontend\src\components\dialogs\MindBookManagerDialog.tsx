/**
 * MindBookManagerDialog.tsx
 * 
 * Comprehensive dialog for managing MindBooks - the unified replacement for Project Manager.
 * This provides a modern interface for saving, loading, organizing, and managing
 * complete MindBook sessions (collections of MindSheets).
 */

import React, { useState, useEffect } from 'react';
import {
  saveMindBook,
  loadMindBook,
  deleteMindBook,
  getMindBooksList,
  getSessionInfo
} from '../../core/services/MindBookPersistenceService';
import { useMindBookStore } from '../../core/state/MindBookStore';
import './MindBookManagerDialog.css';

interface MindBookManagerDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MindBookItem {
  id: string;
  name: string;
  savedAt: number;
  sheetsCount: number;
  description?: string;
}

const MindBookManagerDialog: React.FC<MindBookManagerDialogProps> = ({ isOpen, onClose }) => {
  // State
  const [activeTab, setActiveTab] = useState<'save' | 'open' | 'organize'>('save');
  const [mindBooks, setMindBooks] = useState<MindBookItem[]>([]);
  const [newMindBookName, setNewMindBookName] = useState('');
  const [newMindBookDescription, setNewMindBookDescription] = useState('');
  const [selectedMindBookId, setSelectedMindBookId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  const mindBookStore = useMindBookStore();
  const sessionInfo = getSessionInfo();

  // Load MindBooks list when dialog opens
  useEffect(() => {
    if (isOpen) {
      refreshMindBooksList();
    }
  }, [isOpen]);

  // Auto-clear messages after 3 seconds
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const refreshMindBooksList = () => {
    try {
      const list = getMindBooksList();
      setMindBooks(list);
    } catch (error) {
      showMessage('error', 'Failed to load MindBooks list');
    }
  };

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
  };

  const handleSaveMindBook = async () => {
    if (!newMindBookName.trim()) {
      showMessage('error', 'Please enter a MindBook name');
      return;
    }

    if (mindBookStore.sheets.length === 0) {
      showMessage('error', 'Cannot save empty MindBook. Create some sheets first.');
      return;
    }

    setIsLoading(true);
    
    try {
      const success = saveMindBook(newMindBookName.trim(), newMindBookDescription.trim() || undefined);
      
      if (success) {
        showMessage('success', `MindBook "${newMindBookName}" saved successfully`);
        setNewMindBookName('');
        setNewMindBookDescription('');
        refreshMindBooksList();
        setActiveTab('open'); // Switch to open tab to see the saved MindBook
      } else {
        showMessage('error', 'Failed to save MindBook');
      }
    } catch (error) {
      showMessage('error', 'Error saving MindBook');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadMindBook = async (mindBookId: string) => {
    setIsLoading(true);
    
    try {
      const success = loadMindBook(mindBookId);
      
      if (success) {
        const mindBook = mindBooks.find(mb => mb.id === mindBookId);
        showMessage('success', `MindBook "${mindBook?.name}" loaded successfully`);
        onClose(); // Close dialog after successful load
      } else {
        showMessage('error', 'Failed to load MindBook');
      }
    } catch (error) {
      showMessage('error', 'Error loading MindBook');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteMindBook = async (mindBookId: string) => {
    const mindBook = mindBooks.find(mb => mb.id === mindBookId);
    
    if (!mindBook) return;
    
    const confirmed = window.confirm(
      `Are you sure you want to delete "${mindBook.name}"?\n\nThis action cannot be undone.`
    );
    
    if (!confirmed) return;
    
    setIsLoading(true);
    
    try {
      const success = deleteMindBook(mindBookId);
      
      if (success) {
        showMessage('success', `MindBook "${mindBook.name}" deleted successfully`);
        refreshMindBooksList();
        if (selectedMindBookId === mindBookId) {
          setSelectedMindBookId(null);
        }
      } else {
        showMessage('error', 'Failed to delete MindBook');
      }
    } catch (error) {
      showMessage('error', 'Error deleting MindBook');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatFileSize = (sheetsCount: number) => {
    if (sheetsCount === 1) return '1 sheet';
    return `${sheetsCount} sheets`;
  };

  if (!isOpen) return null;

  return (
    <div className="mindbook-manager-overlay">
      <div className="mindbook-manager-dialog">
        {/* Header */}
        <div className="mindbook-manager-header">
          <h2>MindBook Manager</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        {/* Tabs */}
        <div className="mindbook-manager-tabs">
          <button
            className={`tab-button ${activeTab === 'save' ? 'active' : ''}`}
            onClick={() => setActiveTab('save')}
          >
            💾 Save
          </button>
          <button
            className={`tab-button ${activeTab === 'open' ? 'active' : ''}`}
            onClick={() => setActiveTab('open')}
          >
            📂 Open
          </button>
          <button
            className={`tab-button ${activeTab === 'organize' ? 'active' : ''}`}
            onClick={() => setActiveTab('organize')}
          >
            🗂️ Organize
          </button>
        </div>

        {/* Message */}
        {message && (
          <div className={`message ${message.type}`}>
            {message.text}
          </div>
        )}

        {/* Content */}
        <div className="mindbook-manager-content">
          {/* Save Tab */}
          {activeTab === 'save' && (
            <div className="save-tab">
              <div className="current-session-info">
                <h3>Current Session</h3>
                <div className="session-stats">
                  <span className="stat">
                    📊 {sessionInfo.sheetsCount} sheets
                  </span>
                  {sessionInfo.hasAutoSave && (
                    <span className="stat auto-saved">
                      💾 Auto-saved
                    </span>
                  )}
                </div>
              </div>

              {sessionInfo.sheetsCount > 0 ? (
                <div className="save-form">
                  <div className="form-group">
                    <label htmlFor="mindbook-name">MindBook Name *</label>
                    <input
                      id="mindbook-name"
                      type="text"
                      value={newMindBookName}
                      onChange={(e) => setNewMindBookName(e.target.value)}
                      placeholder="Enter a name for your MindBook..."
                      disabled={isLoading}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="mindbook-description">Description (optional)</label>
                    <textarea
                      id="mindbook-description"
                      value={newMindBookDescription}
                      onChange={(e) => setNewMindBookDescription(e.target.value)}
                      placeholder="Brief description of what this MindBook contains..."
                      rows={3}
                      disabled={isLoading}
                    />
                  </div>

                  <button
                    className="save-button"
                    onClick={handleSaveMindBook}
                    disabled={isLoading || !newMindBookName.trim()}
                  >
                    {isLoading ? '💾 Saving...' : '💾 Save MindBook'}
                  </button>
                </div>
              ) : (
                <div className="empty-session">
                  <p>No sheets in current session. Create some MindSheets or ChatForks first.</p>
                </div>
              )}
            </div>
          )}

          {/* Open Tab */}
          {activeTab === 'open' && (
            <div className="open-tab">
              <div className="mindbooks-header">
                <h3>Saved MindBooks</h3>
                <button className="refresh-button" onClick={refreshMindBooksList}>
                  🔄 Refresh
                </button>
              </div>

              {mindBooks.length === 0 ? (
                <div className="no-mindbooks">
                  <p>No saved MindBooks found.</p>
                  <p>Save your current session in the <strong>Save</strong> tab to get started.</p>
                </div>
              ) : (
                <div className="mindbooks-list">
                  {mindBooks.map((mindBook) => (
                    <div
                      key={mindBook.id}
                      className={`mindbook-item ${selectedMindBookId === mindBook.id ? 'selected' : ''}`}
                      onClick={() => setSelectedMindBookId(mindBook.id)}
                    >
                      {/* Delete button positioned at top-right corner */}
                      <button
                        className="mindbook-delete-x"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteMindBook(mindBook.id);
                        }}
                        disabled={isLoading}
                        title={`Delete "${mindBook.name}"`}
                        aria-label={`Delete ${mindBook.name}`}
                      >
                        ×
                      </button>

                      <div className="mindbook-info">
                        <div className="mindbook-name">{mindBook.name}</div>
                        <div className="mindbook-meta">
                          <span>{formatFileSize(mindBook.sheetsCount)}</span>
                          <span>•</span>
                          <span>{formatDate(mindBook.savedAt)}</span>
                        </div>
                        {mindBook.description && (
                          <div className="mindbook-description">{mindBook.description}</div>
                        )}
                      </div>
                      <div className="mindbook-actions">
                        <button
                          className="load-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleLoadMindBook(mindBook.id);
                          }}
                          disabled={isLoading}
                        >
                          📂 Open
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Organize Tab */}
          {activeTab === 'organize' && (
            <div className="organize-tab">
              <div className="organize-header">
                <h3>Organize MindBooks</h3>
                <div className="organize-stats">
                  Total: {mindBooks.length} MindBooks
                </div>
              </div>

              {mindBooks.length === 0 ? (
                <div className="no-mindbooks">
                  <p>No MindBooks to organize.</p>
                </div>
              ) : (
                <div className="organize-list">
                  {mindBooks.map((mindBook) => (
                    <div
                      key={mindBook.id}
                      className="organize-item"
                    >
                      <div className="organize-info">
                        <div className="organize-name">{mindBook.name}</div>
                        <div className="organize-meta">
                          {formatFileSize(mindBook.sheetsCount)} • {formatDate(mindBook.savedAt)}
                        </div>
                        {mindBook.description && (
                          <div className="organize-description">{mindBook.description}</div>
                        )}
                      </div>
                      <div className="organize-actions">
                        <button
                          className="delete-button"
                          onClick={() => handleDeleteMindBook(mindBook.id)}
                          disabled={isLoading}
                        >
                          🗑️ Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mindbook-manager-footer">
          <div className="footer-info">
            MindBooks are like Excel workbooks - they contain multiple sheets of different types
          </div>
          <button className="close-footer-button" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default MindBookManagerDialog; 