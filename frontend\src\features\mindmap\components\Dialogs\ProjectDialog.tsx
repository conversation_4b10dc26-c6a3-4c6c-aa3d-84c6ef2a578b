/**
 * ProjectDialog.tsx
 * 
 * Dialog for managing MindBooks (formerly projects).
 */

import React, { useState, useEffect } from 'react';
import {
  saveMindBook,
  getMindBooksList,
  loadMindBook,
  deleteMindBook,
  getSessionInfo,
  type MindBookListItem
} from '../../../../core/services/MindBookPersistenceService';
import { useMindBookStore } from '../../../../core/state/MindBookStore';
import './ProjectDialog.css';

interface ProjectDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProjectDialog: React.FC<ProjectDialogProps> = ({ isOpen, onClose }) => {
  // Local state
  const [mindBooks, setMindBooks] = useState<MindBookListItem[]>([]);
  const [newMindBookName, setNewMindBookName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const mindBookStore = useMindBookStore();
  const sessionInfo = getSessionInfo();
  
  // Load MindBooks from storage
  useEffect(() => {
    if (isOpen) {
      loadMindBooks();
    }
  }, [isOpen]);
  
  // Load MindBooks from storage
  const loadMindBooks = () => {
    try {
      // Get MindBooks from the new system
      const allMindBooks = getMindBooksList();
      
      // Also get legacy mindmap projects
      const legacyProjects: MindBookListItem[] = [];
      
      // Check localStorage for legacy projects
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        
        // Check if key is a legacy mindmap project
        if (key && key.startsWith('mindmap_') && !key.includes('_sheet_')) {
          try {
            const projectData = JSON.parse(localStorage.getItem(key) || '{}');
            const name = key.replace('mindmap_', '');
            
            legacyProjects.push({
              id: `legacy_${name}`, // Prefix to identify as legacy
              name: `${name} (Legacy)`,
              savedAt: projectData.savedAt || Date.now(),
              sheetsCount: Object.keys(projectData.nodes || {}).length,
              description: 'Legacy mindmap project'
            });
          } catch (error) {
            console.error('Error parsing legacy project data:', error);
          }
        }
      }
      
      // Combine and sort all projects
      const allProjects = [...allMindBooks, ...legacyProjects];
      const sorted = allProjects.sort((a, b) => b.savedAt - a.savedAt);
      setMindBooks(sorted);
    } catch (error) {
      console.error('Error loading MindBooks:', error);
      setMindBooks([]);
    }
  };
  
  // Handle save current MindBook (update existing)
  const handleSaveCurrentMindBook = async () => {
    if (mindBookStore.sheets.length === 0) {
      alert('Cannot save empty MindBook. Create some sheets first.');
      return;
    }

    setIsLoading(true);
    try {
      // Use existing name if available, otherwise require new name
      const nameToUse = mindBookStore.name || newMindBookName.trim();

      if (!nameToUse) {
        alert('Please enter a name for your MindBook');
        setIsLoading(false);
        return;
      }

      const success = saveMindBook(nameToUse, undefined, false); // false = don't force new
      if (success) {
        console.log('MindBook saved successfully');
        setNewMindBookName('');
        loadMindBooks(); // Refresh the list
        onClose();
      } else {
        alert('Failed to save MindBook');
      }
    } catch (error) {
      console.error('Error saving MindBook:', error);
      alert('Error saving MindBook');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle save as new MindBook
  const handleSaveAsMindBook = async () => {
    if (!newMindBookName.trim()) {
      alert('Please enter a name for the new MindBook');
      return;
    }

    if (mindBookStore.sheets.length === 0) {
      alert('Cannot save empty MindBook. Create some sheets first.');
      return;
    }

    setIsLoading(true);
    try {
      const success = saveMindBook(newMindBookName.trim(), undefined, true); // true = force new
      if (success) {
        console.log('MindBook saved as new successfully');
        setNewMindBookName('');
        loadMindBooks(); // Refresh the list
        onClose();
      } else {
        alert('Failed to save MindBook');
      }
    } catch (error) {
      console.error('Error saving MindBook:', error);
      alert('Error saving MindBook');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle load MindBook
  const handleLoadMindBook = async (mindBookId: string) => {
    setIsLoading(true);
    try {
      const success = loadMindBook(mindBookId);
      if (success) {
        console.log('MindBook loaded successfully');
        onClose();
      } else {
        alert('Failed to load MindBook');
      }
    } catch (error) {
      console.error('Error loading MindBook:', error);
      alert('Error loading MindBook');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete MindBook
  const handleDeleteMindBook = async (mindBookId: string) => {
    const mindBook = mindBooks.find(mb => mb.id === mindBookId);

    if (!mindBook) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete "${mindBook.name}"?\n\nThis action cannot be undone.`
    );

    if (!confirmed) return;

    setIsLoading(true);

    try {
      const success = deleteMindBook(mindBookId);

      if (success) {
        console.log(`MindBook "${mindBook.name}" deleted successfully`);
        loadMindBooks(); // Refresh the list
      } else {
        alert('Failed to delete MindBook');
      }
    } catch (error) {
      console.error('Error deleting MindBook:', error);
      alert('Error deleting MindBook');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!isOpen) {
    return null;
  }
  
  return (
    <div className="project-dialog-overlay">
      <div className="project-dialog">
        <div className="project-dialog-header">
          <h2>MindBook Library</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="project-dialog-content">
          <div className="new-project">
            <h3>Save MindBook</h3>
            {sessionInfo.sheetsCount > 0 ? (
              <div className="save-section">
                {/* Show current MindBook info if it exists */}
                {mindBookStore.name && !mindBookStore.isNewMindBook() ? (
                  <div className="current-mindbook">
                    <p><strong>Current MindBook:</strong> {mindBookStore.name}</p>
                    <button
                      className="save-button"
                      onClick={handleSaveCurrentMindBook}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                ) : null}

                {/* Save As section */}
                <div className="save-as-section">
                  <h4>{mindBookStore.name && !mindBookStore.isNewMindBook() ? 'Save As New MindBook' : 'Save Current MindBook'}</h4>
                  <div className="new-project-form">
                    <input
                      type="text"
                      placeholder={mindBookStore.name && !mindBookStore.isNewMindBook() ? "Enter new name" : "Name your MindBook"}
                      value={newMindBookName}
                      onChange={(e) => setNewMindBookName(e.target.value)}
                    />
                    <button
                      className="create-button"
                      onClick={mindBookStore.name && !mindBookStore.isNewMindBook() ? handleSaveAsMindBook : handleSaveCurrentMindBook}
                      disabled={!newMindBookName.trim() || isLoading}
                    >
                      {isLoading ? 'Saving...' : (mindBookStore.name && !mindBookStore.isNewMindBook() ? 'Save As' : 'Save')}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <p className="no-sheets">No sheets to save. Create some MindSheets first.</p>
            )}
          </div>
          
          <div className="project-list">
            <h3>Saved MindBooks</h3>
            {mindBooks.length === 0 ? (
              <p className="no-projects">No saved MindBooks found.</p>
            ) : (
              <ul>
                {mindBooks.map((mindBook) => (
                  <li
                    key={mindBook.id}
                    className="project-item"
                    onClick={() => handleLoadMindBook(mindBook.id)}
                  >
                    {/* Delete button positioned at top-right corner */}
                    <button
                      className="project-delete-x"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteMindBook(mindBook.id);
                      }}
                      disabled={isLoading}
                      title={`Delete "${mindBook.name}"`}
                      aria-label={`Delete ${mindBook.name}`}
                    >
                      ×
                    </button>

                    <div className="project-item-info">
                      <span className="project-item-name">{mindBook.name}</span>
                      <span className="project-item-meta">
                        {mindBook.sheetsCount} sheets • {new Date(mindBook.savedAt).toLocaleString()}
                        {mindBook.description && (
                          <span className="project-description"> • {mindBook.description}</span>
                        )}
                      </span>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDialog;
